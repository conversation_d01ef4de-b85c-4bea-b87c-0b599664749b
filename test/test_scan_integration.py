"""
集群扫描集成测试
验证完整的扫描流程和组件协作
"""

import os
import sys
import asyncio
import unittest
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
os.environ.update({
    'CACHE_DB_PATH': './test_data/test_integration_cache.db',
    'CACHE_DB_TIMEOUT': '10',
    'CACHE_STATIC_TTL': '1800',
    'CACHE_DYNAMIC_TTL': '300'
})

from src.scanner import ClusterScanner, ResourceParser, ScanCoordinator
from src.cache import CacheManager
from src.mcp_tools import MCPToolLoader


class TestScanIntegration(unittest.TestCase):
    """扫描集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的MCP客户端
        self.mock_mcp_client = Mock()
        self.mock_mcp_client.run = AsyncMock()
        
        # 创建模拟的工具加载器
        self.mock_tool_loader = Mock()
        self.mock_tool_loader.get_tool_capabilities = AsyncMock()
        
        # 创建缓存管理器
        self.cache_manager = CacheManager()
        
        # 创建扫描组件
        self.cluster_scanner = ClusterScanner(
            mcp_client=self.mock_mcp_client,
            tool_loader=self.mock_tool_loader,
            timeout=30,
            max_retries=2
        )
        
        self.resource_parser = ResourceParser()
        
        self.scan_coordinator = ScanCoordinator(
            cluster_scanner=self.cluster_scanner,
            resource_parser=self.resource_parser,
            cache_manager=self.cache_manager,
            static_ttl=1800,
            dynamic_ttl=300
        )
    
    async def test_full_scan_integration(self):
        """测试完整扫描集成流程"""
        # 模拟工具能力检查
        self.mock_tool_loader.get_tool_capabilities.return_value = Mock()
        
        # 模拟完整的MCP工具调用结果
        mock_responses = [
            # 静态资源响应
            {  # 集群信息
                'name': 'integration-test-cluster',
                'version': 'v1.23.15',
                'server': 'https://test-cluster:6443',
                'nodeCount': 2
            },
            {  # 命名空间
                'items': [
                    {
                        'metadata': {'name': 'default'},
                        'status': {'phase': 'Active'}
                    },
                    {
                        'metadata': {'name': 'kube-system'},
                        'status': {'phase': 'Active'}
                    }
                ]
            },
            {  # 节点
                'items': [
                    {
                        'metadata': {
                            'name': 'master-node',
                            'labels': {'node-role.kubernetes.io/master': ''}
                        },
                        'status': {
                            'conditions': [{'type': 'Ready', 'status': 'True'}],
                            'capacity': {'cpu': '4', 'memory': '8Gi'},
                            'allocatable': {'cpu': '3800m', 'memory': '7.5Gi'}
                        },
                        'spec': {'taints': []}
                    },
                    {
                        'metadata': {
                            'name': 'worker-node',
                            'labels': {'node-role.kubernetes.io/worker': ''}
                        },
                        'status': {
                            'conditions': [{'type': 'Ready', 'status': 'True'}],
                            'capacity': {'cpu': '2', 'memory': '4Gi'},
                            'allocatable': {'cpu': '1800m', 'memory': '3.5Gi'}
                        },
                        'spec': {'taints': []}
                    }
                ]
            },
            # 动态资源响应
            {  # Pod
                'items': [
                    {
                        'metadata': {
                            'name': 'web-app-123',
                            'namespace': 'default',
                            'labels': {'app': 'web'}
                        },
                        'status': {
                            'phase': 'Running',
                            'containerStatuses': [
                                {'name': 'web', 'ready': True, 'restartCount': 0}
                            ]
                        },
                        'spec': {
                            'nodeName': 'worker-node',
                            'containers': [
                                {'name': 'web', 'image': 'nginx:1.21'}
                            ]
                        }
                    }
                ]
            },
            {  # 服务
                'items': [
                    {
                        'metadata': {'name': 'web-service', 'namespace': 'default'},
                        'spec': {
                            'type': 'ClusterIP',
                            'clusterIP': '*********',
                            'selector': {'app': 'web'},
                            'ports': [{'port': 80, 'targetPort': 8080}]
                        }
                    }
                ]
            },
            {  # 部署
                'items': [
                    {
                        'metadata': {'name': 'web-deployment', 'namespace': 'default'},
                        'spec': {'replicas': 1, 'selector': {'matchLabels': {'app': 'web'}}},
                        'status': {'readyReplicas': 1, 'availableReplicas': 1}
                    }
                ]
            },
            {  # ConfigMap
                'items': [
                    {
                        'metadata': {'name': 'app-config', 'namespace': 'default'},
                        'data': {'config.yaml': 'app: web\nport: 8080'},
                        'binaryData': {}
                    }
                ]
            },
            {  # Secret
                'items': [
                    {
                        'metadata': {'name': 'app-secret', 'namespace': 'default'},
                        'data': {'password': 'cGFzc3dvcmQ='},
                        'binaryData': {}
                    }
                ]
            }
        ]
        
        # 配置MCP客户端返回值
        self.mock_mcp_client.run.side_effect = mock_responses
        
        # 执行完整集群扫描
        result = await self.scan_coordinator.scan_cluster_full(
            cluster_name='integration-test-cluster',
            include_static=True,
            include_dynamic=True
        )
        
        # 验证扫描结果
        self.assertEqual(result['cluster_name'], 'integration-test-cluster')
        self.assertIn('static_resources', result)
        self.assertIn('dynamic_resources', result)
        self.assertIn('statistics', result)
        
        # 验证静态资源扫描结果
        static_result = result['static_resources']
        self.assertTrue(static_result['success'])
        static_data = static_result['data']
        self.assertEqual(static_data['clusters'], 1)
        self.assertEqual(static_data['namespaces'], 2)
        self.assertEqual(static_data['nodes'], 2)
        
        # 验证动态资源扫描结果
        dynamic_result = result['dynamic_resources']
        self.assertTrue(dynamic_result['success'])
        dynamic_data = dynamic_result['data']
        self.assertEqual(dynamic_data['pods'], 1)
        self.assertEqual(dynamic_data['services'], 1)
        self.assertEqual(dynamic_data['configmaps'], 1)
        self.assertEqual(dynamic_data['secrets'], 1)
        
        # 验证统计信息
        stats = result['statistics']
        self.assertEqual(stats['total_resources'], 8)  # 1+2+2+1+1+1 = 8
        self.assertEqual(stats['static_resources'], 5)  # 1+2+2 = 5
        self.assertEqual(stats['dynamic_resources'], 3)  # 1+1+1 = 3 (不包括secret)
        
        print("✅ 完整扫描集成测试通过")
    
    async def test_health_check_integration(self):
        """测试健康检查集成"""
        # 执行健康检查
        health_result = await self.scan_coordinator.health_check()
        
        # 验证健康检查结果
        self.assertIn('status', health_result)
        self.assertIn('timestamp', health_result)
        self.assertIn('components', health_result)
        self.assertIn('issues', health_result)
        
        # 验证组件状态
        components = health_result['components']
        self.assertIn('cache_manager', components)
        self.assertIn('cluster_scanner', components)
        self.assertIn('resource_parser', components)
        
        print("✅ 健康检查集成测试通过")
    
    async def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 模拟工具不存在错误
        self.mock_tool_loader.get_tool_capabilities.return_value = None
        
        # 执行扫描并验证错误处理
        result = await self.scan_coordinator.scan_cluster_full(
            cluster_name='error-test-cluster',
            include_static=True,
            include_dynamic=False
        )
        
        # 验证错误被正确处理
        self.assertEqual(result['cluster_name'], 'error-test-cluster')
        static_result = result['static_resources']
        self.assertFalse(static_result['success'])
        self.assertIn('error', static_result)
        
        print("✅ 错误处理集成测试通过")
    
    def test_cache_integration(self):
        """测试缓存集成"""
        # 创建测试数据
        from src.cache.models import ClusterInfo, NamespaceInfo
        
        cluster_info = ClusterInfo(
            name='cache-test-cluster',
            version='v1.23.15',
            api_server='https://test:6443',
            node_count=1
        )
        
        namespace_info = NamespaceInfo(
            cluster_name='cache-test-cluster',
            name='test-namespace',
            status='Active',
            labels={'env': 'test'},
            annotations={}
        )
        
        # 写入缓存
        self.cache_manager.create_record('clusters', cluster_info, ttl=1800)
        self.cache_manager.create_record('namespaces', namespace_info, ttl=1800)
        
        # 从缓存读取
        cached_clusters = self.cache_manager.list_records('clusters')
        cached_namespaces = self.cache_manager.list_records('namespaces')
        
        # 验证缓存数据
        self.assertGreater(len(cached_clusters), 0)
        self.assertGreater(len(cached_namespaces), 0)
        
        # 验证数据内容
        found_cluster = False
        for cluster in cached_clusters:
            if cluster.get('name') == 'cache-test-cluster':
                found_cluster = True
                self.assertEqual(cluster.get('version'), 'v1.23.15')
                break
        
        self.assertTrue(found_cluster, "未找到缓存的集群信息")
        
        print("✅ 缓存集成测试通过")


async def run_integration_tests():
    """运行集成测试"""
    print("🧪 开始运行集群扫描集成测试...")
    
    # 创建测试实例
    test_instance = TestScanIntegration()
    test_instance.setUp()
    
    try:
        # 运行异步集成测试
        await test_instance.test_full_scan_integration()
        await test_instance.test_health_check_integration()
        await test_instance.test_error_handling_integration()
        
        # 运行同步测试
        test_instance.test_cache_integration()
        
        print("✅ 所有集成测试通过!")
        
        # 显示组件统计
        print("\n📊 组件统计信息:")
        coordinator_stats = test_instance.scan_coordinator.get_coordinator_stats()
        print(f"   - 扫描会话: {coordinator_stats['scan_sessions']}")
        print(f"   - 成功扫描: {coordinator_stats['successful_scans']}")
        print(f"   - 失败扫描: {coordinator_stats['failed_scans']}")
        print(f"   - 成功率: {coordinator_stats['success_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("🚀 启动集群扫描集成测试")
    asyncio.run(run_integration_tests())
